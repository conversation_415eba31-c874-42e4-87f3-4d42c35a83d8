<script>
    import { H2, P1, P2, <PERSON><PERSON> } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    import SignUpButton from '$lib/landingPage/SignUpButton.svelte';
    import phucImage from '$lib/assets/phuc.png?enhanced';
    
    let { 
        sectionLabel = "Manifesto",
        headline = "We want you to self-study to 1600",
        founderName = "Phuc",
        companyName = "DSAT16",
        introText = "In 2023, I self-studied to 1600 Digital SAT. With the SAT, I got into my dream university (Aalto). I truly believe it is the best ticket for anyone (yes, even you!) to get into the university of their dreams. ",
        backstoryText = "The consensus seems to be that you cannot self-study for the SAT. Well, I'm building DSAT16 to prove that it's not true.",
        beliefStatement = "I'm here to tell you that you don't need to be smart and grind 12 hours a day to succeed. DSAT16 is built to help you:",
        helpItems = [
            "Keeping you motivated and engaged with learning",
            "See clear improvements over time",
            "Get admitted, win that scholarship, and change your life"
        ],
        primaryButton = "Join the Movement",
        secondaryButton = "More about us",
        closingText = "If that resonates with you, you're in the right place. As DSAT16 is still in beta, you get to try it for free. So, try the product out, leave some feedback, and help us shape the future of SAT prep.",
        signature = "- Phuc"
    } = $props();
</script>

<SectionWrapper --bg-color="var(--light-purple)" --padding-top="8rem" --padding-bottom="8rem">
<div class="manifesto-section">
    <P2>{sectionLabel}</P2>
    <!-- Top section with text and image side by side -->
    <div class="manifesto-top">
        <div class="manifesto-intro">
            <H2>{@html headline}</H2>

            <div class="intro-text">
                <P1 isBold>Hey, I'm {@html founderName}. I'm the CEO, CTO, CFO, C-3PO, and Senior Programmer of {@html companyName}. (Did you know you can just make up titles?)</P1>
                <P1>{introText}</P1>
                <P1>{backstoryText}</P1>
            </div>
                <P1>{@html beliefStatement}</P1>
        </div>

        <div class="manifesto-image">
            <enhanced:img src={phucImage} alt="Phuc - Founder of DSAT16" class="founder-image" loading="lazy" />
        </div>
    </div>

    <!-- Bottom section with full-width content -->
    <div class="manifesto-bottom">
        <div class="manifesto-content">

            <div class="manifesto-list">
                {#each helpItems as item}
                    <div class="list-item">
                        <div class="arrow-icon">→</div>
                        <P1>{item}</P1>
                    </div>
                {/each}
            </div>

            <P1>{closingText}</P1>
            <P1><strong>{signature}</strong></P1>

            <div class="manifesto-buttons">
                <SignUpButton>{primaryButton}</SignUpButton>
                <!-- <Button isSecondary>{secondaryButton}</Button> -->
            </div>
        </div>
    </div>
</div>
</SectionWrapper>

<style>
    /* Manifesto Section */
    .manifesto-section {
        display: flex;
        flex-direction: column;
                width: 100%;
    }

    /* Top section with text and image side by side */
    .manifesto-top {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 4rem;
        align-items: flex-start;
    }

    .manifesto-intro {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .intro-text {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    /* Bottom section with full-width content */
    .manifesto-bottom {
        width: 100%;
    }

    .manifesto-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }
    
    .manifesto-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin: 1rem 0;
    }
    
    .list-item {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .arrow-icon {
        background: var(--tangerine);
        color: white;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.2rem;
        flex-shrink: 0;
    }
    
    .manifesto-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .manifesto-image {
        display: flex;
        justify-content: center;
    }
    
    .founder-image {
        width: 100%;
        height: 25rem;
        object-fit: cover;
        border: 0.25rem solid var(--pitch-black);
        border-radius: 0.5rem;
    }
    
    /* Mobile Responsiveness */
    @media (max-width: 48rem) {
        .manifesto-section {
            gap: 2rem;
        }

        .manifesto-top {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
    }
</style>
